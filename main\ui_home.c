#include "lvgl.h"
#include "led_ws2812.h"
#include "dht11.h"
#include "esp_log.h"

LV_IMG_DECLARE(temperture)
LV_IMG_DECLARE(water)

#define ws2812_NUM 12

static lv_obj_t *temp_image;
static lv_obj_t *humidity_image;

static lv_obj_t *temp_label;
static lv_obj_t *humidity_label;

static lv_obj_t *light_slider;

static lv_timer_t* dht11_timer;

ws2812_strip_handle_t ws2812_handle;

void lv_slider_event_cb(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch(code)
    {
        case LV_EVENT_VALUE_CHANGED:
        {
            lv_obj_t * slider = lv_event_get_target(e);
            uint32_t value = lv_slider_get_value(slider);
            uint32_t rgb_value =150*value/100;
            for(int led_index = 0;led_index<ws2812_NUM;led_index++)
            {
                ws2812_write(ws2812_handle,led_index,rgb_value,rgb_value,rgb_value);
            }
            break;
        }
        default:
            break;
    }
}

void dht11_timer_cb(struct _lv_timer_t *t)
{
    int temp;
    int humidity;
    if(DHT11_StartGet(&temp,&humidity))
    {
        char disp_buf[32];
        snprintf(disp_buf,sizeof(disp_buf),"%.1f",(float)temp/10.0);
        lv_label_set_text(temp_label,disp_buf);

        snprintf(disp_buf,sizeof(disp_buf),"%d%%",humidity);
        lv_label_set_text(humidity_label,disp_buf); 
    }

}
void ui_home_create(void)
{
    light_slider = lv_slider_create(lv_scr_act());
    lv_obj_set_pos(light_slider, 60, 200);
    lv_obj_set_size(light_slider, 150, 15);
    lv_slider_set_range(light_slider, 0, 100);
    lv_obj_add_event_cb(light_slider, lv_slider_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

    temp_image = lv_img_create(lv_scr_act());
    lv_img_set_src(temp_image, &temperture);
    lv_obj_set_pos(temp_image,  40, 40);    

    humidity_image = lv_img_create(lv_scr_act());
    lv_img_set_src(humidity_image, &water);
    lv_obj_set_pos(humidity_image,  40, 110);    

    temp_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(temp_label,110,40);

    humidity_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(temp_label,110,100);

    //创建定时器
    dht11_timer = lv_timer_create(dht11_timer_cb,2000,NULL);

    //初始化WS2812

    ws2812_init(GPIO_NUM_32,ws2812_NUM,&ws2812_handle);

    //初始化dht11
    DHT11_Init(GPIO_NUM_25);

}