{"version": "1.2", "project_name": "lvgldisplay", "project_version": "1", "project_path": "E:/esp_code/lvgldisplay", "idf_path": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2", "build_dir": "E:/esp_code/lvgldisplay/build", "config_file": "E:/esp_code/lvgldisplay/sdkconfig", "config_defaults": "", "bootloader_elf": "E:/esp_code/lvgldisplay/build/bootloader/bootloader.elf", "app_elf": "lvgldisplay.elf", "app_bin": "lvgldisplay.bin", "build_type": "flash_app", "git_revision": "v5.4.2-dirty", "target": "esp32", "rev": "0", "min_rev": "0", "max_rev": "399", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32-elf-", "c_compiler": "E:/espidf/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bt/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_jpeg/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_touch_sens/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_tsens/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/openthread/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ulp/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/usb/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/Kconfig;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/Kconfig;E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/Kconfig.projbuild;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/Kconfig.projbuild;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/Kconfig.projbuild;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py/Kconfig.projbuild;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bsp", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lvgl__lvgl", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_update", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support", "E:/esp_code/lvgldisplay/managed_components/bsp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bt", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cmock", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_jpeg", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ppa", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdio", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_touch_sens", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_tsens", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif_stack", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_vfs_console", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/http_parser", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/idf_test", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/json", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip", "E:/esp_code/lvgldisplay/main", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/openthread", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protobuf-c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/rt", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ulp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/usb", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/app_trace/libapp_trace.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/app_trace.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/app_trace_util.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/host_file_io.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/app_update/libapp_update.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_update/esp_ota_ops.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_common_loader.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_clock_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_mem.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_random.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_efuse.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/flash_encrypt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/secure_boot.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_random_esp32.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/bootloader_utility.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/flash_partitions.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/esp_image_format.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/idf/bootloader_sha.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/src/esp32/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bsp": {"alias": "idf::bsp", "target": "___idf_bsp", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/managed_components/bsp", "type": "LIBRARY", "lib": "__idf_bsp", "reqs": ["esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/bsp/libbsp.a", "sources": ["E:/esp_code/lvgldisplay/managed_components/bsp/cst816t_driver.c", "E:/esp_code/lvgldisplay/managed_components/bsp/st7789_driver.c"], "include_dirs": ["."]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/cmock/libcmock.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/console/libconsole.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/commands.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/esp_console_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/split_argv.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/linenoise/linenoise.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/esp_console_repl_chip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_cmd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_date.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_dbl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_dstr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_end.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_file.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_hashtable.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_int.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_lit.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_rem.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_rex.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_str.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/arg_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console/argtable3/argtable3.c"], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/cxx/libcxx.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx/cxx_exception_stubs.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx/cxx_guards.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/driver/libdriver.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/adc_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/adc_dma_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/dac_common_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/esp32/dac_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/timer_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/i2c/i2c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/i2s_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/mcpwm_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/pcnt_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/rmt_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/sigma_delta_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/touch_sensor/touch_sensor_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/touch_sensor/esp32/touch_sensor.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/twai/twai.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/deprecated/adc_i2s_deprecated.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/efuse/libefuse.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/esp_efuse_table.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/esp_efuse_fields.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/esp_efuse_utility.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/src/esp_efuse_api.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/src/esp_efuse_fields.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/src/esp_efuse_utility.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/esp_tls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/esp_tls_error_capture.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/esp_tls_platform_port.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/adc_oneshot.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/adc_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/adc_cali.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/adc_cali_curve_fitting.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/adc_continuous.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/esp32/adc_dma.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/esp32/adc_cali_line_fitting.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/deprecated/esp32/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex/esp32/esp_coex_adapter.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex/src/coexist_debug_diagram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_common/libesp_common.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam/esp_cam_ctlr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac", "type": "LIBRARY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_dac/libesp_driver_dac.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/dac_oneshot.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/dac_cosine.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/dac_continuous.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/dac_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/esp32/dac_dma.c"], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio/src/gpio.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio/src/rtc_io.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer/src/gptimer.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c/i2c_master.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c/i2c_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s/i2s_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s/i2s_std.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s/i2s_pdm.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_cap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_com.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_fault.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_gen.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_oper.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_sync.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/src/rmt_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/src/rmt_encoder.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/src/rmt_rx.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdio", "type": "LIBRARY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_sdio/libesp_driver_sdio.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdio/src/sdio_slave.c"], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi/src/sdspi_crc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi/src/sdspi_host.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/src/gpspi/spi_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/src/gpspi/spi_master.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/src/gpspi/spi_slave.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/src/gpspi/spi_dma.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_tsens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart/src/uart.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/esp_eth.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/esp_eth_netif_glue.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/mac/esp_eth_mac_esp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/mac/esp_eth_mac_esp_dma.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/mac/esp_eth_mac_esp_gpio.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_generic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_dp83848.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_ip101.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_ksz80xx.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_lan87xx.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/src/phy/esp_eth_phy_rtl8201.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_event/libesp_event.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event/default_event_loop.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event/esp_event.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/gdbstub.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/gdbstub_transport.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/packet.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid/src/esp_hidd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid/src/esp_hidh.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client/esp_http_client.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client/lib/http_auth.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client/lib/http_header.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_main.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_parse.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_sess.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_txrx.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_uri.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/httpd_ws.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/cpu.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/esp_cpu_intr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/esp_memory_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/cpu_region_protect.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/esp_clk.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/clk_ctrl_os.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/hw_random.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/intr_alloc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/mac_addr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/periph_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/revision.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/rtc_module.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_modem.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_modes.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_console.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_usb.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_gpio.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_event.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/regi2c_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/esp_gpio_reserve.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sar_periph_ctrl_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/io_mux.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/esp_clk_tree.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp_clk_tree_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/esp_dma_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/gdma_link.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/spi_share_hw_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/spi_bus_lock.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/clk_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/adc_share_hw_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/rtc_wdt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/mspi_timing_tuning.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sleep_wake_stub.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/esp_clock_output.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/rtc_clk.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/rtc_clk_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/rtc_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/rtc_sleep.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/rtc_time.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/chip_info.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/cache_sram_mmu.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/sar_periph_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/lowpower"], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_panel_io.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_panel_st7789.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/src/esp_lcd_panel_ops.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/i80/esp_lcd_panel_io_i2s.c"], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl/src/esp_local_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/esp_mmu_map.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/port/esp32/ext_mem_layout.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/esp_cache.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/cache_esp32.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/esp_netif_handlers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/esp_netif_objects.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/esp_netif_defaults.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/esp_netif_lwip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/esp_netif_sntp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/netif/wlanif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/netif/ethernetif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition/partition.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/src/phy_override.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/src/lib_printf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/src/phy_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/src/phy_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/esp32/phy_init_data.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm/pm_locks.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm/pm_trace.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram", "type": "CONFIG_ONLY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_sys.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_print.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_crc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_uart.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_spiflash.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_efuse.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_gpio.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/patches/esp_rom_longjmp.S"], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_security/libesp_security.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/src/init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/src/esp_crypto_lock.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_system/libesp_system.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/esp_err.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/crosscore_int.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/esp_ipc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/freertos_hooks.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/int_wdt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/panic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/esp_system.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/startup.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/startup_funcs.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/system_time.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/stack_check.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/ubsan.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/xt_wdt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/task_wdt/task_wdt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/cpu_start.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/panic_handler.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/esp_system_chip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/image_process.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/brownout.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/esp_ipc_isr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/panic_arch.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/expression_with_stack.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/debug_helpers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/debug_stubs.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/arch/xtensa/trax.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/soc/esp32/highint_hdl.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/soc/esp32/clk.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/soc/esp32/reset_reason.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/soc/esp32/system_internal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/port/soc/esp32/cache_err_int.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/esp_timer.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/esp_timer_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/ets_timer_legacy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/system_time.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/esp_timer_impl_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer/src/esp_timer_impl_lac.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/lib_printf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/mesh_event.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/smartconfig.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/wifi_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/wifi_default.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/wifi_netif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/wifi_default_ap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/esp32/esp_adapter.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_flash.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_uart.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_elf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_binary.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_sha.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/core_dump_crc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/fatfs/libfatfs.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/diskio/diskio.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/diskio/diskio_rawflash.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/diskio/diskio_wl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/src/ff.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/src/ffunicode.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/port/freertos/ffsystem.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/diskio/diskio_sdmmc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/vfs/vfs_fat.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/vfs/vfs_fat_sdmmc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/freertos/libfreertos.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/heap_idf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/app_startup.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/port_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/port_systick.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/list.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/queue.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/tasks.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/timers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/event_groups.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/esp_additions/freertos_compatibility.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/esp_additions/idf_additions_event_groups.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/hal/libhal.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/hal_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/mpu_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/efuse_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/efuse_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/wdt_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/mmu_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/cache_hal_esp32.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/color_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_flash_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_flash_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_flash_encrypt_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/clk_tree_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/uart_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/uart_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/gpio_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/rtc_io_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/timer_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/ledc_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/ledc_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/i2c_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/i2c_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/rmt_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/pcnt_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/mcpwm_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/twai_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/twai_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/i2s_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/sdm_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/sdmmc_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/emac_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/adc_hal_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/adc_oneshot_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/adc_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/mpi_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/sha_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/aes_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/brownout_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_slave_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/spi_slave_hal_iram.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/sdio_slave_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/touch_sensor_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/touch_sensor_hal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/gpio_hal_workaround.c"], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/heap/libheap.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/heap_caps_base.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/heap_caps.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/heap_caps_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/multi_heap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/tlsf/tlsf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/port/memory_layout_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/port/esp32/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/json/libjson.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/json/cJSON/cJSON.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/log/liblog.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/os/log_timestamp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/log_timestamp_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/os/log_lock.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/os/log_write.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/buffer/log_buffers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/util.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/log_level/log_level.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/log_level/tag_log_level/tag_log_level.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl", "type": "LIBRARY", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/lvgl__lvgl/liblvgl__lvgl.a", "sources": ["E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_disp.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_event.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_group.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_indev.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_indev_scroll.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_class.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_draw.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_pos.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_scroll.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_style.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_style_gen.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_obj_tree.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_refr.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/core/lv_theme.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/arm2d/lv_gpu_arm2d.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_arc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_img.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_label.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_layer.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_line.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_mask.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_rect.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_transform.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_draw_triangle.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_img_buf.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_img_cache.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/lv_img_decoder.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_buf.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_utils.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/renesas/lv_gpu_d2_draw_label.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_arc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_bg.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_composite.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_img.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_label.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_layer.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_line.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_mask.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_polygon.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_rect.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_utils.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_arc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_blend.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_dither.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_gradient.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_img.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_layer.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_letter.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_line.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_polygon.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_rect.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_transform.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/layouts/flex/lv_flex.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/layouts/grid/lv_grid.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/bmp/lv_bmp.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/freetype/lv_freetype.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_fatfs.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_littlefs.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_posix.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_stdio.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_win32.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/gif/gifdec.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/gif/lv_gif.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/png/lodepng.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/png/lv_png.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/qrcode/lv_qrcode.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/qrcode/qrcodegen.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/rlottie/lv_rlottie.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/sjpg/lv_sjpg.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/sjpg/tjpgd.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/libs/tiny_ttf/lv_tiny_ttf.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/lv_extra.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/fragment/lv_fragment.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/fragment/lv_fragment_manager.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/gridnav/lv_gridnav.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/ime/lv_ime_pinyin.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/imgfont/lv_imgfont.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/monkey/lv_monkey.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/msg/lv_msg.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/others/snapshot/lv_snapshot.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/themes/basic/lv_theme_basic.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/themes/default/lv_theme_default.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/themes/mono/lv_theme_mono.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/animimg/lv_animimg.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/chart/lv_chart.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/colorwheel/lv_colorwheel.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/imgbtn/lv_imgbtn.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/keyboard/lv_keyboard.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/led/lv_led.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/list/lv_list.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/menu/lv_menu.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/meter/lv_meter.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/msgbox/lv_msgbox.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/span/lv_span.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/spinbox/lv_spinbox.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/spinner/lv_spinner.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/tabview/lv_tabview.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/tileview/lv_tileview.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/extra/widgets/win/lv_win.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_fmt_txt.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_loader.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_10.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_12.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_12_subpx.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_14.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_16.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_18.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_20.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_22.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_24.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_26.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28_compressed.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_30.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_32.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_34.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_36.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_38.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_40.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_42.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_44.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_46.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_48.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_8.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_simsun_16_cjk.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_unscii_16.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/font/lv_font_unscii_8.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/hal/lv_hal_disp.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/hal/lv_hal_indev.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/hal/lv_hal_tick.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_anim.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_anim_timeline.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_area.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_async.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_bidi.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_color.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_fs.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_gc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_ll.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_log.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_lru.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_math.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_mem.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_printf.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_style.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_style_gen.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_templ.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_timer.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_tlsf.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_txt.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_txt_ap.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/misc/lv_utils.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_arc.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_bar.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_btn.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_btnmatrix.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_canvas.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_checkbox.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_dropdown.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_img.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_label.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_line.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_objx_templ.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_roller.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_slider.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_switch.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_table.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src/widgets/lv_textarea.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/anim/lv_example_anim_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/anim/lv_example_anim_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/anim/lv_example_anim_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/anim/lv_example_anim_timeline_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/animimg001.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/animimg002.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/animimg003.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/emoji/img_emoji_F617.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_caret_down.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_cogwheel_alpha16.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_cogwheel_argb.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_cogwheel_chroma_keyed.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_cogwheel_indexed16.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_cogwheel_rgb.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_hand.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_skew_strip.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/img_star.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/imgbtn_left.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/imgbtn_mid.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/assets/imgbtn_right.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/event/lv_example_event_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/event/lv_example_event_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/event/lv_example_event_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/event/lv_example_event_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/get_started/lv_example_get_started_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/get_started/lv_example_get_started_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/get_started/lv_example_get_started_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/flex/lv_example_flex_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/layouts/grid/lv_example_grid_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/bmp/lv_example_bmp_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/freetype/lv_example_freetype_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/gif/img_bulb_gif.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/gif/lv_example_gif_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/png/img_wink_png.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/png/lv_example_png_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/qrcode/lv_example_qrcode_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/rlottie/lv_example_rlottie_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/rlottie/lv_example_rlottie_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/rlottie/lv_example_rlottie_approve.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/libs/sjpg/lv_example_sjpg_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/fragment/lv_example_fragment_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/fragment/lv_example_fragment_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/gridnav/lv_example_gridnav_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/gridnav/lv_example_gridnav_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/gridnav/lv_example_gridnav_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/gridnav/lv_example_gridnav_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/ime/lv_example_ime_pinyin_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/ime/lv_example_ime_pinyin_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/imgfont/lv_example_imgfont_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/monkey/lv_example_monkey_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/monkey/lv_example_monkey_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/monkey/lv_example_monkey_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/msg/lv_example_msg_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/msg/lv_example_msg_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/msg/lv_example_msg_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/others/snapshot/lv_example_snapshot_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/porting/lv_port_disp_template.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/porting/lv_port_fs_template.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/porting/lv_port_indev_template.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/scroll/lv_example_scroll_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_10.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_11.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_12.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_13.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_14.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_15.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_7.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_8.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/styles/lv_example_style_9.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/animimg/lv_example_animimg_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/arc/lv_example_arc_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/arc/lv_example_arc_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/bar/lv_example_bar_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btn/lv_example_btn_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btn/lv_example_btn_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btn/lv_example_btn_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/calendar/lv_example_calendar_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/canvas/lv_example_canvas_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/canvas/lv_example_canvas_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_6.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_7.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_8.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/chart/lv_example_chart_9.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/checkbox/lv_example_checkbox_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/checkbox/lv_example_checkbox_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/colorwheel/lv_example_colorwheel_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/dropdown/lv_example_dropdown_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/dropdown/lv_example_dropdown_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/dropdown/lv_example_dropdown_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/img/lv_example_img_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/img/lv_example_img_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/img/lv_example_img_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/img/lv_example_img_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/imgbtn/lv_example_imgbtn_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/keyboard/lv_example_keyboard_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/label/lv_example_label_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/label/lv_example_label_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/label/lv_example_label_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/label/lv_example_label_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/label/lv_example_label_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/led/lv_example_led_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/line/lv_example_line_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/list/lv_example_list_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/list/lv_example_list_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/menu/lv_example_menu_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/menu/lv_example_menu_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/menu/lv_example_menu_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/menu/lv_example_menu_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/menu/lv_example_menu_5.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/meter/lv_example_meter_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/meter/lv_example_meter_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/meter/lv_example_meter_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/meter/lv_example_meter_4.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/msgbox/lv_example_msgbox_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/obj/lv_example_obj_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/obj/lv_example_obj_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/roller/lv_example_roller_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/roller/lv_example_roller_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/roller/lv_example_roller_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/slider/lv_example_slider_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/slider/lv_example_slider_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/slider/lv_example_slider_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/span/lv_example_span_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/spinbox/lv_example_spinbox_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/spinner/lv_example_spinner_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/switch/lv_example_switch_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/table/lv_example_table_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/table/lv_example_table_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/tabview/lv_example_tabview_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/tabview/lv_example_tabview_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/textarea/lv_example_textarea_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/textarea/lv_example_textarea_2.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/textarea/lv_example_textarea_3.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/tileview/lv_example_tileview_1.c", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples/widgets/win/lv_example_win_1.c"], "include_dirs": ["E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/../", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/demos"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/lwip/liblwip.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/apps/sntp/sntp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/api_lib.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/api_msg.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/err.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/if_api.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/netbuf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/netdb.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/netifapi.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/sockets.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/api/tcpip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/apps/sntp/sntp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/def.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/dns.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/inet_chksum.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/mem.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/memp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/netif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/pbuf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/raw.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/stats.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/sys.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/tcp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/tcp_in.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/tcp_out.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/timeouts.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/udp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/autoip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/dhcp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/etharp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/icmp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/igmp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/ip4.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/dhcp6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/ethip6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/icmp6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/inet6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/ip6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/mld6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/core/ipv6/nd6.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ethernet.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/bridgeif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/bridgeif_fdb.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/slipif.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/auth.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/ccp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/chap-md5.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/chap-new.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/chap_ms.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/demand.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/eap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/ecp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/eui64.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/fsm.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/ipcp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/lcp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/magic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/mppe.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/multilink.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/ppp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/pppapi.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/pppoe.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/pppos.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/upap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/vj.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/hooks/tcp_isn_default.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/hooks/lwip_default_hooks.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/debug/lwip_debug.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/sockets_ext.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/freertos/sys_arch.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/acd_dhcp_check.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/port/esp32xx/vfs_lwip.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/apps/ping/esp_ping.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/apps/ping/ping.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/apps/ping/ping_sock.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["lvgl__lvgl"], "file": "E:/esp_code/lvgldisplay/build/esp-idf/main/libmain.a", "sources": ["E:/esp_code/lvgldisplay/main/lvgldisplay.c", "E:/esp_code/lvgldisplay/main/lv_port.h"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "E:/esp_code/lvgldisplay/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/mqtt/libmqtt.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/mqtt_client.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/newlib/libnewlib.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/abort.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/assert.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/heap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/flockfile.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/locks.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/poll.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/pthread.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/random.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/getentropy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/reent_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/newlib_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/syscalls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/termios.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/stdatomic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/time.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/sysconf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/realpath.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/scandir.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_api.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_cxx_api.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_item_hash_list.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_page.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_pagemanager.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_storage.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_handle_simple.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_handle_locked.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_partition.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_partition_lookup.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_partition_manager.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_types.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_platform.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_bootloader.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/perfmon/libperfmon.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon/xtensa_perfmon_access.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon/xtensa_perfmon_apis.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/protocomm/libprotocomm.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/common/protocomm.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/proto-c/constants.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/proto-c/sec0.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/proto-c/sec1.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/proto-c/sec2.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/proto-c/session.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/transports/protocomm_console.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/transports/protocomm_httpd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/security/security0.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/security/security1.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/security/security2.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/crypto/srp6a/esp_srp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/pthread/libpthread.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/pthread.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/pthread_cond_var.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/pthread_local_storage.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/pthread_rwlock.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/rt/librt.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/rt/FreeRTOS_POSIX_mqueue.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_cmd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_io.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_mmc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sdmmc_sd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/soc/libsoc.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/lldesc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/dport_access_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/interrupts.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/gpio_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/uart_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/dport_access.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/adc_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/emac_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/spi_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/ledc_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/pcnt_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/rmt_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/sdm_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/i2s_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/i2c_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/timer_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/lcd_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/mcpwm_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/mpi_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/sdmmc_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/touch_sensor_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/twai_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/wdt_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/dac_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/rtc_io_periph.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/sdio_slave_periph.c"], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/flash_brownout_hook.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_drivers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_generic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_issi.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_mxic.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_gd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_winbond.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_boya.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_mxic_opi.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_chip_th.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/memspi_host_driver.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/cache_utils.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/flash_mmap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/flash_ops.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_wrap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/esp_flash_api.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/esp_flash_spi_init.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_os_func_app.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/spiffs/libspiffs.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs_api.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs/src/spiffs_cache.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs/src/spiffs_check.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs/src/spiffs_gc.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs/src/spiffs_hydrogen.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/spiffs/src/spiffs_nucleus.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/transport.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/transport_ssl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/transport_internal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/transport_socks_proxy.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/unity/libunity.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity/src/unity.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_compat.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_runner.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_utils_freertos.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_utils_cache.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_utils_memory.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/unity_port_esp32.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/usb", "type": "CONFIG_ONLY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/vfs/libvfs.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/vfs.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/vfs_eventfd.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/vfs_semihost.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/Partition.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/SPI_Flash.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/WL_Ext_Perf.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/WL_Ext_Safe.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/WL_Flash.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/crc32.cpp", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/wifi_config.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/wifi_scan.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/wifi_ctrl.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/manager.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/handlers.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/scheme_console.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/port/os_xtensa.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/port/eloop.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/ap_config.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/ieee802_1x.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/wpa_auth.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/sta_info.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/ieee802_11.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/ap/comeback_token.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/common/sae.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/common/dragonfly.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/common/wpa_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/bitfield.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/aes-siv.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha256-kdf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/ccmp.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/aes-gcm.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/crypto_ops.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/dh_group5.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/dh_groups.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/ms_funcs.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha256-prf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha1-prf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha384-prf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/md4-internal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/sha1-tprf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/common/ieee802_11_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/chap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_peap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_tls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/mschapv2.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_fast.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/rsn_supp/wpa.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/base64.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/ext_password.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/uuid.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/wpabuf.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/wpa_debug.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/utils/json.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_attr_build.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_attr_parse.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_attr_process.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_dev_attr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/wps/wps_enrollee.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/common/sae_pk.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/rc4.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/des-internal.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/aes-wrap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/aes-unwrap.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp_code/lvgldisplay/build/esp-idf/xtensa/libxtensa.a", "sources": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/eri.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/xt_trax.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/xtensa_context.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/xtensa_intr_asm.S", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/xtensa_intr.c", "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/touch_element", "lib": "__idf_touch_element", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/main", "lib": "__idf_main", "reqs": [], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["lvgl__lvgl"], "include_dirs": ["."]}, "bsp": {"alias": "idf::bsp", "target": "___idf_bsp", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/managed_components/bsp", "lib": "__idf_bsp", "reqs": ["esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/src", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/../", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/examples", "E:/esp_code/lvgldisplay/managed_components/lvgl__lvgl/demos"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "E:/esp_code/lvgldisplay/build/gdbinit/symbols", "02_prefix_map": "E:/esp_code/lvgldisplay/build/gdbinit/prefix_map", "03_py_extensions": "E:/esp_code/lvgldisplay/build/gdbinit/py_extensions", "04_connect": "E:/esp_code/lvgldisplay/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32-wrover-kit-3.3v.cfg"}