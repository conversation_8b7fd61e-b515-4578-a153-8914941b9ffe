# Load esp32 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x3ff9ea80, "Jun  8 2016")
  if (*(int*) 0x3ff9ea80) == 0x206e754a && (*(int*) 0x3ff9ea84) == 0x32203820 && (*(int*) 0x3ff9ea88) == 0x363130
    add-symbol-file E:/espidf/Espressif/tools/esp-rom-elfs/20241011/esp32_rev0_rom.elf
  else
    # if $_streq((char *) 0x3ff9e986, "Jul 29 2019")
    if (*(int*) 0x3ff9e986) == 0x206c754a && (*(int*) 0x3ff9e98a) == 0x32203932 && (*(int*) 0x3ff9e98e) == 0x393130
      add-symbol-file E:/espidf/Espressif/tools/esp-rom-elfs/20241011/esp32_rev300_rom.elf
    else
      echo Warning: Unknown esp32 ROM revision.\n
    end
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    # Bootloader elf was not found
set confirm on

# Load application symbols
file E:/esp_code/lvgldisplay/build/bootloader/bootloader.elf
