#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lvgl.h"
#include "esp_log.h"
#include "st7789_driver.h"
#include "cst816t_driver.h"
#include "driver/gpio.h"
#include "esp_timer.h"
static lv_disp_drv_t disp_drv;

#define LCD_WIDTH 240
#define LCD_HEIGHT 280
#define TAG "lv_port"

void lv_flush_done_cb(void* param)
{
    lv_disp_flush_ready(&disp_drv);
}

void disp_flush(struct _lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    st7789_flush( area->x1, area->x2+1, area->y1+20, area->y2+20+1, color_p);
}

void lv_disp_init(void)
{
    static lv_disp_draw_buf_t disp_buf;
    const size_t disp_buf_size = LCD_WIDTH*(LCD_HEIGHT/7);



    lv_color_t *disp1 = heap_caps_malloc(disp_buf_size*sizeof(lv_color_t), MALLOC_CAP_DMA|MALLOC_CAP_INTERNAL);
    lv_color_t *disp2 = heap_caps_malloc(disp_buf_size*sizeof(lv_color_t), MALLOC_CAP_DMA|MALLOC_CAP_INTERNAL);

    if(!disp1 || !disp2)
    {
        ESP_LOGE(TAG,"disp1 or disp2 malloc failed");
        return;
    }
    lv_disp_draw_buf_init(&disp_buf, disp1, disp2, disp_buf_size);

    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = LCD_WIDTH;
    disp_drv.ver_res = LCD_HEIGHT;
    disp_drv.draw_buf = &disp_buf;

    // disp_drv.flush_cb = disp_flush;

    // disp_drv.user_data = &disp_drv;
    disp_drv.flush_cb = disp_flush;
    lv_disp_drv_register(&disp_drv);
}
void IRAM_ATTR indev_read(struct _lv_indev_drv_t * indev_drv, lv_indev_data_t * data)
{
    int16_t x,y;
    int state;
    cst816t_read(&x,&y,&state);
    data->point.x = x;
    data->point.y = y;
    data->state = state;
}

void lv_indev_init(void)
{
    static lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = indev_read;
    lv_indev_drv_register(&indev_drv);
}

void st7789_init(void)
{
    st7789_cfg_t cfg={
        .bl = GPIO_NUM_26,
        .clk = GPIO_NUM_18,
        .cs = GPIO_NUM_5,
        .dc = GPIO_NUM_17,
        .mosi = GPIO_NUM_19,
        .rst = GPIO_NUM_21,
        .spi_fre = 40*1000*1000,
        .height = LCD_HEIGHT,
        .width = LCD_WIDTH,
        .spin = 0,
        .done_cb = lv_flush_done_cb,
        .cb_param = &disp_drv,
    
    };

    st7789_driver_hw_init(&cfg);
}

void cst816t_hw_init(void)
{
    cst816t_cfg_t cst816t_config = {
    
        .scl = GPIO_NUM_22,
        .sda = GPIO_NUM_23,
        .fre = 300*1000,
        .x_limit = LCD_WIDTH,
        .y_limit = LCD_HEIGHT,
    };
    cst816t_init(&cst816t_config);
}

void lv_timer_cb(void* arg)
{
    uint32_t tick_increment = *((uint32_t*)arg);
    lv_tick_inc(tick_increment);
}

void lv_tick_init(void)
{
    static uint32_t tick_increment = 5;
    const esp_timer_create_args_t periodic_timer_args = {
        .arg = &tick_increment,
        .callback = lv_timer_cb,
        .name = "periodic_gui",
        .dispatch_method = ESP_TIMER_TASK,
        .skip_unhandled_events = true,
    };
    esp_timer_handle_t periodic_timer;
    esp_timer_create(&periodic_timer_args, &periodic_timer);
    esp_timer_start_periodic(periodic_timer, tick_increment *1000);
}

void lv_port_init(void)
{
    // 初始化LVGL库
    lv_init();

    // 初始化显示和触摸硬件
    st7789_init();
    cst816t_hw_init();

    // 初始化LVGL的显示、输入设备和时钟
    lv_disp_init();
    lv_indev_init();
    lv_tick_init();
}