#include <stdio.h>
#include "lvgl.h"
#include "lv_port.h"
#include "lv_demos.h"
#include "cst816t_driver.h"
#include "st7789_driver.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "driver/gpio.h"
#include "freertos/task.h"
#include "ui_led.h"
#include "ui_home.h"
void app_main(void)
{
    
    lv_port_init();
    st7789_lcd_backlight(1);
    #if 0
 //   lv_demo_widgets();
    ui_led_create();

    gpio_config_t io_conf=
    {
        .pull_up_en = GPIO_PULLUP_DISABLE,          //禁止上拉
        .pull_down_en = GPIO_PULLDOWN_DISABLE,      //禁止下拉
        .mode = GPIO_MODE_OUTPUT,                   //输出模式
        .intr_type = GPIO_INTR_DISABLE,             //禁止中断
        .pin_bit_mask = (1<<GPIO_NUM_27)             //GPIO脚
    };

    gpio_config(&io_conf);
    gpio_set_level(GPIO_NUM_27,0);
    #endif
    ui_home_create();
    while(1)
    {
        lv_task_handler();

        vTaskDelay(pdMS_TO_TICKS(10));
    }
}