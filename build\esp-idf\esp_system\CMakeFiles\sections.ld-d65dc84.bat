@echo off
cd /D E:\esp_code\lvgldisplay\build\esp-idf\esp_system || (set FAIL_LINE=2& goto :ABORT)
E:\espidf\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/tools/ldgen/ldgen.py --config E:/esp_code/lvgldisplay/sdkconfig --fragments-list E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/app.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/common.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/soc.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/hal/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/log/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/heap/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/soc/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/linker_common.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/newlib.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/system_libs.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/driver/twai/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/vfs/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/lwip/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154/linker.lf;E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/components/openthread/linker.lf --input E:/esp_code/lvgldisplay/build/esp-idf/esp_system/ld/sections.ld.in --output E:/esp_code/lvgldisplay/build/esp-idf/esp_system/ld/sections.ld --kconfig E:/espidf/Espressif/frameworks/esp-idf-v5.4.2/Kconfig --env-file E:/esp_code/lvgldisplay/build/config.env --libraries-file E:/esp_code/lvgldisplay/build/ldgen_libraries --objdump E:/espidf/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-objdump.exe || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%